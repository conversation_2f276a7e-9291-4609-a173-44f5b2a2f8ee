import Image, { StaticImageData } from "next/image";
import clsx from "clsx";
import Link from "next/link";

interface BlogsCardProps {
  image: string | StaticImageData;
  tags?: string[];
  title: string;
  description?: string;
  date: string;
  showDescription?: boolean;
  className?: string;
  slug?: string;
}

export default function BlogsCard({
  image,
  tags = [],
  title,
  description,
  date,
  showDescription = true,
  className = "",
  slug,
}: BlogsCardProps) {
  return (
    <Link
      href={`/blogs/${slug}`}
      rel="noopener noreferrer"
      className={clsx("rounded-3xl transition-all duration-300", className)}
    >
      <div
        style={{ boxShadow: "inset 0px 0px 50px 9px rgba(0, 0, 0, 0.5)" }}
        className="border-white-100 hover:border-white-800 group relative mb-[18px] h-[330px] w-full overflow-hidden rounded-3xl border-[4px] transition-all duration-300 hover:shadow-lg"
      >
        <Image
          src={image}
          alt="Blog Cover"
          layout="fill"
          className="object-cover brightness-[0.4] grayscale transition-all duration-300 group-hover:brightness-100 group-hover:grayscale-0"
        />
      </div>

      {!!tags?.length && (
        <div className="mb-[18px] flex flex-wrap gap-2 text-[12px] font-normal leading-[18px]">
          {tags?.map((tag: string, index: number) => (
            <span
              key={index}
              className="bg-white-100 border-white-100 rounded border px-2 py-0.5"
            >
              {tag}
            </span>
          ))}
        </div>
      )}

      <h2 className="mb-[12px] line-clamp-2 text-[24px] font-medium leading-6 md:mb-[18px] md:text-[32px] md:font-semibold md:leading-[120%]">
        {title}
      </h2>

      {showDescription && description && (
        <h3 className="text-white-500 mb-[12px] line-clamp-3 text-[12px] font-normal leading-[18px] md:mb-[18px] md:text-[16px] md:font-medium md:leading-6">
          {description}
        </h3>
      )}

      <p className="text-[12px] font-normal leading-[18px] md:text-[18px] md:leading-[120%]">
        {date}
      </p>
    </Link>
  );
}
