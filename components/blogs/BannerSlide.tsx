"use client";

import { getBlogImageUrl } from "@/utils/blog";
import BlogsCard from "./BlogsCard";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import "swiper/css";
import { useRef, useState } from "react";
import { ArrowLeftIcon, ArrowRightIcon } from "@/assets/icons";

export const BannerSlide = ({ blogs }: { blogs: any[] }) => {
  const [currentPage, setCurrentPage] = useState(0);
  const swiperRef = useRef<any>(null);

  return (
    <div className="desktop:mr-[calc(-50vw+50%)] mr-0 w-full overflow-x-hidden">
      <Swiper
        modules={[Navigation]}
        slidesPerView={"auto"}
        spaceBetween={56}
        className="!pr-[46px]"
        onSwiper={(swiper) => (swiperRef.current = swiper)}
        onSlideChange={(swiper) => setCurrentPage(swiper.activeIndex)}
      >
        {blogs.slice(0, 4)?.map((post, i) => (
          <SwiperSlide key={i} className="!w-[343px] lg:!w-[430px]">
            <BlogsCard
              image={getBlogImageUrl(
                post?.attributes?.cover?.data?.attributes?.formats?.medium?.url
              )}
              slug={post?.attributes?.slug}
              title={post?.attributes?.title}
              date={
                post?.attributes?.publishedAt
                  ? new Date(post?.attributes?.publishedAt).toLocaleDateString(
                      "en-US",
                      {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      }
                    )
                  : ""
              }
              showDescription={false}
              className="w-full"
            />
          </SwiperSlide>
        ))}
      </Swiper>

      {!!blogs.length && (
        <div className="mt-4 flex items-center justify-center gap-4 md:mt-8 md:justify-start">
          <div
            className="bg-white-100 hover:bg-brand-500 flex h-[48px] w-[48px] cursor-pointer items-center justify-center rounded-full sm:h-[56px] sm:w-[56px]"
            onClick={() => swiperRef.current?.slidePrev()}
          >
            <ArrowLeftIcon className="hover:text-black-900 group-hover:text-black-900 [&>*]:fill-current" />
          </div>
          <div className="heading-lg-medium-24">
            {currentPage + 1} / {blogs.slice(0, 4).length}
          </div>
          <div
            className="bg-white-100 hover:bg-brand-500 flex h-[48px] w-[48px] cursor-pointer items-center justify-center rounded-full sm:h-[56px] sm:w-[56px]"
            onClick={() => swiperRef.current?.slideNext()}
          >
            <ArrowRightIcon className="hover:text-black-900" />
          </div>
        </div>
      )}
    </div>
  );
};
