import { SuiIcon, SuiIconBase } from "@/assets/icons";
import React from "react";
import { NETWORKS } from "@/utils/contants";

const HypeIcon = ({ className }: { className?: string }) => (
  <div
    className={`flex items-center justify-center rounded-full bg-purple-500 text-xs font-bold text-white ${className}`}
  >
    H
  </div>
);

const SomiIcon = ({ className }: { className?: string }) => (
  <div
    className={`flex items-center justify-center rounded-full bg-orange-500 text-xs font-bold text-white ${className}`}
  >
    S
  </div>
);

export const AppLogoNetwork = ({
  network,
  className,
  isBase = false,
}: {
  network: string;
  className?: string;
  isBase?: boolean;
}) => {
  if (network === NETWORKS.SUI) {
    if (isBase) {
      return <SuiIconBase className={className} />;
    }
    return <SuiIcon className={className} />;
  }

  if (network === NETWORKS.HYPE) {
    return <HypeIcon className={className} />;
  }

  if (network === NETWORKS.SOMI) {
    return <SomiIcon className={className} />;
  }

  return <div />;
};
