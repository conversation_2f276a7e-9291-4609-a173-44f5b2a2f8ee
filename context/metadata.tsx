import React, {
  createContext,
  memo,
  ReactNode,
  useEffect,
  useRef,
} from "react";
import { useDispatch, useSelector } from "react-redux";
import { Dispatch } from "redux";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import rf from "@/services/RequestFactory";
import { AppDispatch, RootState } from "@/store";
import {
  getDexes,
  getTrendingPairsMeme,
  setQuotePrice,
  setTrendingPairMeme,
} from "@/store/metadata.store";
import { TPair } from "@/types";
import { NETWORKS, SUI_TOKEN_ADDRESS_FULL } from "@/utils/contants";
import { useNetwork } from "./network";

type MetadataContextType = Record<string, never>;

const MetadataContext = createContext<MetadataContextType>({});

const TrendingPairsHandler = memo(() => {
  const { currentNetwork } = useNetwork();
  const trendingPairsMeme = useSelector(
    (state: RootState) => state.metadata.trendingPairsMeme
  );
  const trendingPairsMemeRef = useRef<TPair[]>([]);

  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    trendingPairsMemeRef.current = trendingPairsMeme;
  }, [trendingPairsMeme]);

  const handleWhenPairStatsChange = async (event: TBroadcastEvent) => {
    const data = event.detail;

    if (!trendingPairsMemeRef?.current) return;

    const trendingPairIndex = trendingPairsMemeRef?.current?.findIndex(
      (pair) => pair.pairId === data.pairId
    );
    if (trendingPairIndex !== -1 && trendingPairsMemeRef?.current) {
      const updatedPairs = [...trendingPairsMemeRef.current];
      updatedPairs[trendingPairIndex] = {
        ...updatedPairs[trendingPairIndex],
        stats: {
          ...updatedPairs[trendingPairIndex].stats,
          ...data,
        },
      };
      trendingPairsMemeRef.current = updatedPairs;
      dispatch(setTrendingPairMeme(updatedPairs));
      return;
    }
    return;
  };

  useEffect(() => {
    dispatch(getTrendingPairsMeme({ network: currentNetwork }));
    AppBroadcast.on(
      BROADCAST_EVENTS.PAIR_STATS_UPDATED,
      handleWhenPairStatsChange
    );
    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.PAIR_STATS_UPDATED,
        handleWhenPairStatsChange
      );
    };
  }, []);

  return null;
});

TrendingPairsHandler.displayName = "TrendingPairsHandler";

const PriceHandler = memo(() => {
  const QUOTE_TOKEN_ADDRESSES = [
    SUI_TOKEN_ADDRESS_FULL,
    "0xbc732bc5f1e9a9f4bdf4c0672ee538dbf56c161afe04ff1de2176efabdf41f92::suai::SUAI",
    "0xdba34672e30cb065b1f93e3ab55318768fd6fef66c15942c9f7cb846e2f900e7::usdc::USDC",
  ];
  const dispatch = useDispatch<Dispatch>();
  const { currentNetwork } = useNetwork();

  const fetchQuotePriceWithRetry = async (
    tokenAddress: string,
    maxRetries = 3
  ) => {
    let attempt = 1;
    while (attempt <= maxRetries) {
      try {
        const coinData = await rf
          .getRequest("PriceRequest")
          .getCoinPrice(currentNetwork, tokenAddress);

        if (!coinData || !coinData.priceUsd) {
          throw new Error(`No coin data received for token ${tokenAddress}`);
        }

        dispatch(
          setQuotePrice({
            tokenAddress: coinData.address,
            price: coinData.price,
            priceUsd: coinData.priceUsd,
          })
        );
        return;
      } catch (error) {
        console.warn(
          `Attempt ${attempt} failed for token ${tokenAddress}:`,
          error
        );

        if (attempt === maxRetries) {
          console.error(
            `Failed to fetch price for token ${tokenAddress} after ${maxRetries} attempts`
          );
          return;
        }

        await new Promise((resolve) => setTimeout(resolve, 1000));
        attempt++;
      }
    }
  };

  useEffect(() => {
    const fetchQuotePrices = async () => {
      await Promise.all(
        QUOTE_TOKEN_ADDRESSES.map(async (tokenAddress) => {
          await fetchQuotePriceWithRetry(tokenAddress);
        })
      );
    };
    fetchQuotePrices();
    const intervalId = setInterval(async () => {
      fetchQuotePrices();
    }, 60000);

    return () => {
      clearInterval(intervalId);
    };
  }, [dispatch]);
  return null;
});
PriceHandler.displayName = "PriceHandler";

export const MetadataProvider = ({ children }: { children: ReactNode }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { currentNetwork } = useNetwork();

  useEffect(() => {
    dispatch(getDexes({ network: currentNetwork }));
  }, [currentNetwork, dispatch]);

  return (
    <MetadataContext.Provider value={{}}>
      <TrendingPairsHandler />
      <PriceHandler />
      {children}
    </MetadataContext.Provider>
  );
};
