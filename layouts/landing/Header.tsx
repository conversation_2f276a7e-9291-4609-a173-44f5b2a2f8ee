"use client";

import React, { useRef, useState, useEffect } from "react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import {
  Logo,
  Telegram,
  MenuIcon,
  ChevronDownIcon,
  TwitterIcon,
  InstallAppIcon,
  CloseAuditCheckIcon,
} from "@/assets/icons";
import { AppButton } from "@/components";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@/store";
import Storage from "@/libs/storage";
import config from "@/config";
import { Profile } from "@/layouts/Header";
import { useMediaQuery } from "react-responsive";
import AppDrawer from "@/components/AppDrawer";
import { isAndroidMobile } from "@/utils/helper";
import ButtonInstallPWA from "@/components/ButtonInstallPWA";
import { scroller } from "react-scroll";
import { setIsHideInstallApp } from "@/store/metadata.store";
import { ROUTE_PATH } from "@/routes";
import { AppDispatch } from "@/store";
import { useLogin } from "../../hooks/useLogin";

export const InstallApp = () => {
  const dispatch = useDispatch<AppDispatch>();

  if (isAndroidMobile()) {
    return <ButtonInstallPWA isInHeader />;
  }

  return (
    <div
      className="tablet:hidden flex items-center justify-between px-4 py-2"
      style={{
        backdropFilter: "blur(calc(var(--16, 16px) / 2))",
        background:
          "linear-gradient(0deg, var(--Brand-900, rgba(0, 204, 163, 0.10)) 0%, var(--Brand-900, rgba(0, 204, 163, 0.10)) 100%), var(--White-50, rgba(255, 255, 255, 0.05))",
      }}
    >
      <div className="body-sm-regular-12">Trade anytime, anywhere!</div>
      <div className="flex items-center gap-2">
        <Link href="/mobile-app">
          <AppButton size="small" variant="buy" className="gap-2">
            <InstallAppIcon />
            Install app
          </AppButton>
        </Link>

        <div
          className="p-2"
          onClick={() => {
            Storage.setIsHideInstallApp(true);
            dispatch(setIsHideInstallApp({ isShow: true }));
          }}
        >
          <CloseAuditCheckIcon />
        </div>
      </div>
    </div>
  );
};

const MobileMenu = ({
  handleScrollToSection,
  isOpen,
  onClose,
}: {
  handleScrollToSection: (id: string) => void;
  onClose: () => void;
  isOpen: boolean;
}) => {
  const { onLogin } = useLogin();

  return (
    <AppDrawer
      toggleDrawer={onClose}
      isOpen={isOpen}
      direction={"left"}
      className="!shadow-menu-mobile !w-[300px]"
    >
      <div className="relative flex h-full flex-col justify-between">
        <div>
          <div className="border-white-100 border-b px-2 py-1.5">
            <Logo />
          </div>
          <div className="border-white-100 border-b p-4">
            <div className="heading-md-medium-18">Welcome to RaidenX</div>
            <div className="body-md-regular-14 text-white-500 mb-4">
              Experience lightning-fast indexing and customizalble
            </div>

            <AppButton className="gap-2" size={"medium"} onClick={onLogin}>
              Connect
            </AppButton>
          </div>

          <div className="flex flex-1 flex-col overflow-y-auto pl-4">
            {MENU.map((item: any, index) => {
              if (item.sub) {
                return (
                  <div key={index}>
                    <div
                      key={index}
                      className="body-md-regular-14 text-white-0 cursor-pointer p-2 px-[6px]"
                    >
                      {item.name}
                    </div>
                    <div className="ml-7">
                      {item.sub.map((s: any, i: number) => {
                        if (s.path) {
                          return (
                            <a
                              key={i}
                              href={s?.path}
                              target={item?.target || "_self"}
                              onClick={() => {
                                onClose();
                              }}
                            >
                              <div className="body-md-regular-14 text-white-800 cursor-pointer p-2 px-[6px]">
                                {s.name}
                              </div>
                            </a>
                          );
                        }
                        return (
                          <div
                            key={i}
                            className="body-md-regular-14 text-white-800 cursor-pointer p-2 px-[6px]"
                            onClick={() => {
                              onClose();
                              handleScrollToSection(s?.id);
                            }}
                          >
                            {s.name}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                );
              }

              if (item?.path) {
                return (
                  <Link href={item?.path} key={index}>
                    <div className="body-md-regular-14 text-white-0 cursor-pointer p-2 px-[6px]">
                      {item.name}
                    </div>
                  </Link>
                );
              }

              return (
                <div
                  key={index}
                  className="body-md-regular-14 text-white-0 cursor-pointer p-2 px-[6px]"
                  onClick={() => {
                    onClose();
                    handleScrollToSection(item?.id);
                  }}
                >
                  {item.name}
                </div>
              );
            })}
          </div>
        </div>

        <div className="border-white-100 text-white-500 flex items-center justify-between border-t px-4 py-3">
          <div className="body-md-regular-14">Join us</div>

          <div className="flex items-center gap-2.5">
            <a href={config.linkSocial.twitter}>
              <TwitterIcon className="h-5 w-5" />
            </a>
            <a href={config.linkSocial.telegram}>
              <Telegram className="h-5 w-5" />
            </a>
          </div>
        </div>
      </div>
    </AppDrawer>
  );
};

const SubMenu = ({
  menu,
  handleScrollToSection,
}: {
  menu: any;
  handleScrollToSection: (id: string) => void;
}) => {
  const [isShow, setIsShow] = useState(false);
  const contentRef = useRef<any>(null);

  const onToggleMenu = () => {
    setIsShow(!isShow);
  };

  const handleClickOutside = (event: Event) => {
    if (contentRef.current && !contentRef.current.contains(event.target)) {
      setIsShow(false);
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside, true);
    return () => {
      document.removeEventListener("click", handleClickOutside, true);
    };
  }, []);

  return (
    <div className="relative">
      <div
        onClick={onToggleMenu}
        className="block flex cursor-pointer items-center gap-2"
      >
        {menu?.name}{" "}
        <ChevronDownIcon
          className={`${
            isShow ? "rotate-[180deg]" : ""
          } 'w-4 duration-[600]' h-4 transition-all`}
        />
      </div>

      {isShow && (
        <div
          ref={contentRef}
          style={{
            boxShadow: "4px 4px 8px 0px var(--Black-500, rgba(8, 9, 12, 0.50))",
            background:
              "linear-gradient(0deg, var(--White-100, rgba(255, 255, 255, 0.10)) 0%, var(--White-100, rgba(255, 255, 255, 0.10)) 100%), var(--Black-900, #08090C)",
          }}
          className="z-9999 absolute left-0 top-[120%] w-[140px] rounded-[8px] p-1"
        >
          <div className="flex flex-col">
            {menu?.sub?.map((item: any, index: number) => {
              if (item?.path) {
                return (
                  <a
                    onClick={() => {
                      setIsShow(false);
                    }}
                    key={index}
                    href={item?.path}
                    target={item?.target || "_self"}
                  >
                    <div className="body-sm-regular-12 text-white-800 hover:text-white-1000 cursor-pointer p-2 px-[6px]">
                      {item?.name}
                    </div>
                  </a>
                );
              }
              return (
                <div
                  onClick={() => {
                    handleScrollToSection(item?.id);
                    setIsShow(false);
                  }}
                  key={index}
                  className="body-sm-regular-12 text-white-800 hover:text-white-1000 cursor-pointer p-2 px-[6px]"
                >
                  {item?.name}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

const MENU = [
  {
    name: "Products",
    sub: [
      {
        name: "Trading Terminal",
        id: "products",
      },
      {
        name: "Premium Signals",
        id: "premium-signals",
      },
      {
        name: "Buy Bot",
        id: "buy-bot",
      },
      {
        name: "Developers Portal",
        id: "developers",
      },
      {
        name: "Data Services",
        id: "data-services",
      },
    ],
  },
  {
    name: "Roadmap",
    id: "road-map",
  },
  {
    name: "Airdrop",
    id: "airdrop",
  },
  {
    name: "About Us",
    path: ROUTE_PATH.ABOUT_US,
  },
  {
    name: "Resources",
    sub: [
      {
        name: "User Guide",
        path: config.homePage.link.userGuide,
        target: "_blank",
      },
      {
        name: "API Docs",
        path: config.homePage.link.apiDocs,
        target: "_blank",
      },
      {
        name: "Brand Kit",
        path: config.homePage.link.brandKit,
        target: "_blank",
      },
      {
        name: "Blog",
        path: "/blogs",
        target: "_self",
      },
    ],
  },
  {
    name: "Mobile App",
    id: "mobile-friendly",
  },
];

export const Header = () => {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isExternalWallet = useSelector(
    (state: RootState) => state.user.isExternalWallet
  );
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const pathname = usePathname();
  const isHideInstallApp = useSelector(
    (state: RootState) => state.metadata.isHideInstallApp
  );
  const [isShow, setIsShow] = useState(false);
  const router = useRouter();
  const { onLogin } = useLogin();

  useEffect(() => {
    if ((isExternalWallet || accessToken) && pathname === ROUTE_PATH.HOME) {
      router.push("/new-pairs");
    }
  }, [accessToken, pathname, isExternalWallet]);

  const isMobileAppPage =
    pathname?.includes("/mobile-app") || pathname?.includes("/webchart");

  const handleScrollToSection = (sectionId: string) => {
    scroller.scrollTo(sectionId, {
      duration: 500,
      delay: 100,
      smooth: true,
      offset: -80, // Scrolls to element + 50 pixels down the page
    });
  };

  return (
    <div
      className="fixed left-0 right-0 top-0 z-[999] w-screen"
      style={{
        backdropFilter: "blur(7.5px)",
      }}
    >
      {!isHideInstallApp && !isMobileAppPage && <InstallApp />}

      <div className="bg-white-50 border-white-100 flex justify-between border-b ">
        <div className="tablet:px-[32px] tablet:py-[16px] mx-auto flex w-full max-w-[1440px] items-center justify-between px-4 py-2">
          <div className="flex items-center gap-0">
            <div className="tablet:hidden block">
              <div
                onClick={() => setIsShow(true)}
                className="tablet:hidden block cursor-pointer p-2"
              >
                <MenuIcon />
              </div>
            </div>
            <Link href="/">
              <Logo />
            </Link>
          </div>

          <div className="tablet:flex flex hidden items-center gap-6">
            {MENU.map((item: any, index) => {
              if (item?.sub) {
                return (
                  <SubMenu
                    menu={item}
                    key={index}
                    handleScrollToSection={handleScrollToSection}
                  />
                );
              }

              if (item?.path) {
                return (
                  <Link href={item?.path} key={index}>
                    <div
                      className={`body-md-medium-14 hover:text-white-1000 cursor-pointer px-[4px] py-3 ${
                        pathname === item.path
                          ? "border-white-0 text-white-900 border-b"
                          : "text-white-800"
                      }`}
                    >
                      {item.name}
                    </div>
                  </Link>
                );
              }

              return (
                <div
                  key={index}
                  className="body-md-medium-14 text-white-800 hover:text-white-1000 cursor-pointer px-[4px]"
                  onClick={() => handleScrollToSection(item.id)}
                >
                  {item.name}
                </div>
              );
            })}
          </div>

          <div className="flex items-center gap-4">
            <a href={config.linkSocial.twitter}>
              <TwitterIcon className="h-5 w-5" />
            </a>
            <a href={config.linkSocial.telegram}>
              <Telegram className="h-5 w-5" />
            </a>
          </div>
        </div>
        <MobileMenu
          isOpen={isShow}
          onClose={() => setIsShow(false)}
          handleScrollToSection={handleScrollToSection}
        />
      </div>
    </div>
  );
};
