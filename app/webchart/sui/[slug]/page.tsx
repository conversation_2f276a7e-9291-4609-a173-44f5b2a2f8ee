"use client";

import * as React from "react";
import { TPair } from "@/types";
import { TradingView } from "@/components/TradingView";
import { useParams, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { normalizeStructTag } from "@mysten/sui/utils";
import rf from "@/services/RequestFactory";
import { NETWORKS } from "@/utils/contants";
import { useNetwork } from "@/context/network";

export default function WebChartPage() {
  const { currentNetwork } = useNetwork();
  const params = useParams();
  const slug = params.slug as string;
  const searchParams = useSearchParams();
  const [externalPair, setExternalPair] = useState<TPair>({} as TPair);

  useEffect(() => {
    const fetchPairData = async () => {
      if (!slug) return;
      const decodedSlug = decodeURIComponent(slug);

      try {
        let res;
        if (
          decodedSlug &&
          decodedSlug.startsWith("0x") &&
          normalizeStructTag(decodedSlug)
        ) {
          res = await rf
            .getRequest("TokenRequest")
            .getTopPair(currentNetwork, decodedSlug);
        } else {
          res = await rf
            .getRequest("PairRequest")
            .getPair(currentNetwork, decodedSlug);
        }

        setExternalPair(res);
      } catch (error) {
        console.error("error", error);
      }
    };

    fetchPairData();
  }, [slug]);

  return (
    <div className="h-screen">
      {externalPair?.pairId && (
        <TradingView
          pair={externalPair}
          device={searchParams.get("device") || "desktop"}
        />
      )}
    </div>
  );
}
