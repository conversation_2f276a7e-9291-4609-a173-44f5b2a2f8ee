import { <PERSON><PERSON>, <PERSON><PERSON> } from "@/layouts/landing";
import React from "react";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="mx-auto flex min-h-screen w-full flex-col  bg-[url('/blogs/bg-blog.png')] bg-cover bg-top bg-no-repeat">
      <Header />
      <div className="flex-1">{children}</div>
      <Footer isHideDataService={true} />
    </div>
  );
}
