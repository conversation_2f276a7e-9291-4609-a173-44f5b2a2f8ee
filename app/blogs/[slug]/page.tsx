import { notFound } from "next/navigation";
import { Metadata } from "next";
import { cache } from "react";
import { getBlogImageUrl } from "@/utils/blog";
import Link from "next/link";
import { ArrowLeftIcon } from "@/assets/icons";
import BlogsCard from "@/components/blogs/BlogsCard";

// Helper function to fetch blog data with caching
const fetchBlogData = cache(async (slug: string) => {
  const token = process.env.STRAPI_ACCESS_TOKEN || "";
  const apiUrl = process.env.STRAPI_API_URL || "";

  const url = `${apiUrl}/blogs/${slug}`;
  try {
    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      next: { revalidate: 60 }, // Cache for 1 minute
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Error fetching blog data:", error);
    return null;
  }
});

// Generate metadata for SEO
export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug: string }>;
}): Promise<Metadata> {
  const { slug } = await params;
  const blogData = await fetchBlogData(slug);

  if (!blogData?.data) {
    return {
      title: "Blog Not Found",
      description: "The requested blog post could not be found.",
    };
  }

  const blog = blogData.data.attributes;
  const imageUrl = blog.cover?.data?.attributes?.url
    ? getBlogImageUrl(blog.cover.data.attributes.url)
    : "https://raidenx.io/open-graph.png";

  return {
    title: `${blog.title}`,
    description:
      blog.description ||
      "Fastest trading & sniping bot on SUI, 1-click buy & sell, realtime chart, audit results, limit orders, DCA, copy trade - only on RaidenX.",
    keywords: [
      "RaidenX",
      "RaidenXTradingTerminal",
      "RaidenXTradingBot",
      "RaidenXTGBot",
      "RaidenXTelegramBot",
      "RaidenXSniperBot",
      "SuiTradingTerminal",
      "SuiTradingBot",
      "SuiMemeCoin",
      "SuiTGBot",
      "SuiTelegramBot",
      "SuiSniperBot",
      "SuiCopyTrade",
      ...(blog.tags?.data || []),
    ].join(", "),
    authors: [{ name: blog.author?.data?.attributes?.name || "RaidenX Team" }],
    openGraph: {
      title: blog.title,
      description: blog.description,
      type: "article",
      url: `https://raidenx.io/blogs/${slug}`,
      images: [
        {
          url: imageUrl,
          width: 1200,
          height: 630,
          alt: blog.title,
        },
      ],
      siteName: "RaidenX",
      publishedTime: blog.publishedAt,
      modifiedTime: blog.updatedAt,
    },
    twitter: {
      card: "summary_large_image",
      title: blog.title,
      description: blog.description,
      images: [imageUrl],
      site: "@raidenx",
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    alternates: {
      canonical: `https://raidenx.io/blogs/${slug}`,
    },
  };
}

export default async function BlogDetailPage({
  params,
}: {
  params: Promise<{ slug: string }>;
}) {
  const { slug } = await params;
  const blogData = await fetchBlogData(slug);

  if (!blogData?.data) {
    return notFound();
  }

  const dataBlog = blogData?.data?.attributes;

  return (
    <div className="mx-auto max-w-[1440px] px-4 pt-[72px] md:px-[90px] md:pt-[160px]">
      {/* Header */}
      <div className="relative z-10 mb-[32px] md:mb-[56px]">
        <Link
          rel="noopener noreferrer"
          href="/blogs"
          className="text-white-1000 mb-4 flex items-center gap-2 text-[18px] font-normal leading-[120%]"
        >
          <ArrowLeftIcon className="h-5 w-5" />
          <span>Back</span>
        </Link>

        <h1 className="mb-4 text-[28px] font-medium leading-[120%] md:text-[40px] md:font-semibold">
          {dataBlog?.title}
        </h1>

        <div className="text-[12px] font-normal leading-4 md:text-[18px] md:leading-[120%]">
          {dataBlog?.publishedAt
            ? new Date(dataBlog?.publishedAt).toLocaleDateString("en-US", {
                year: "numeric",
                month: "long",
                day: "numeric",
              })
            : ""}
        </div>
      </div>

      {/* Main Content + TOC */}
      <div className="mb-[60px] flex flex-col gap-[60px] lg:flex-row lg:gap-[90px]">
        <div className="flex-1">
          <div
            className="blog-content"
            // ref={detailRef}
            dangerouslySetInnerHTML={{
              __html: dataBlog?.content,
            }}
          />
        </div>
      </div>

      {/* Related Posts */}
      {!!dataBlog?.relatedBlogs?.data?.length && (
        <div className="mb-[40px] md:mb-[56px]">
          <h2 className="text-custom-gradient py-[16px] text-[32px] font-medium leading-[40px] md:text-[64px] md:font-bold md:leading-[120%]">
            Related posts
          </h2>
          <div className="grid grid-cols-1 gap-[40px] md:grid-cols-2 lg:grid-cols-3">
            {dataBlog?.relatedBlogs?.data?.map((item: any, index: number) => (
              <div key={index} className="mx-auto w-full lg:w-[383px]">
                <BlogsCard
                  slug={item?.attributes?.slug}
                  image={getBlogImageUrl(
                    item?.attributes?.cover?.data?.attributes?.url
                  )}
                  title={item?.attributes?.title}
                  date={
                    item?.attributes?.publishedAt
                      ? new Date(
                          item?.attributes?.publishedAt
                        ).toLocaleDateString("en-US", {
                          year: "numeric",
                          month: "long",
                          day: "numeric",
                        })
                      : ""
                  }
                />
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
