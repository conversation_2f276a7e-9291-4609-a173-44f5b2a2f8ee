import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { AppButton } from "@/components";
import BlogsCard from "@/components/blogs/BlogsCard";
import { getBlogImageUrl } from "@/utils/blog";
import { BannerSlide } from "@/components/blogs/BannerSlide";

// Generate metadata for SEO
export const metadata: Metadata = {
  title: "Blog | RaidenX - The best trading terminal on SUI",
  description:
    "Fastest trading & sniping bot on SUI, 1-click buy & sell, realtime chart, audit results, limit orders, DCA, copy trade - only on RaidenX",
  keywords: [
    "RaidenX",
    "RaidenXTradingTerminal",
    "RaidenXTradingBot",
    "RaidenXTGBot",
    "RaidenXTelegramBot",
    "RaidenXSniperBot",
    "SuiTradingTerminal",
    "SuiTradingBot",
    "SuiMemeCoin",
    "SuiTGBot",
    "SuiTelegramBot",
    "SuiSniperBot",
    "SuiCopyTrade",
  ].join(", "),
  authors: [{ name: "RaidenX Team" }],
  openGraph: {
    title: "Blog | RaidenX - The best trading terminal on SUI",
    description:
      "Fastest trading & sniping bot on SUI, 1-click buy & sell, realtime chart, audit results, limit orders, DCA, copy trade - only on RaidenX",
    type: "website",
    url: "https://raidenx.io/blogs",
    images: [
      {
        url: "/open-graph.png",
        width: 1200,
        height: 630,
        alt: "RaidenX Blog",
      },
    ],
    siteName: "RaidenX",
  },
  twitter: {
    card: "summary_large_image",
    title: "Blog | RaidenX - The best trading terminal on SUI",
    description:
      "Fastest trading & sniping bot on SUI, 1-click buy & sell, realtime chart, audit results, limit orders, DCA, copy trade - only on RaidenX",
    images: ["https://raidenx.io/open-graph.png"],
    site: "@raidenx",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  alternates: {
    canonical: "https://raidenx.io/blogs",
  },
};

export default async function BlogPage(props: {
  searchParams: Promise<{ page?: string; pageSize?: string }>;
}) {
  const searchParams = await props.searchParams;
  const token = process.env.STRAPI_ACCESS_TOKEN || "";
  const apiUrl = process.env.STRAPI_API_URL || "";

  const page = Number(searchParams.page) || 1;
  const pageSize = Number(searchParams.pageSize) || 9;

  const url = `${apiUrl}/blogs?populate[cover][populate]=*&populate[authors][populate]=*&pagination[page]=${page}&pagination[pageSize]=${pageSize}`;

  const res = await fetch(url, {
    headers: {
      Authorization: `Bearer ${token}`,
      "Content-Type": "application/json",
    },
    next: { revalidate: 1800 },
  });

  const json = await res.json();
  const blogs = json?.data ?? [];
  const pagination = json?.meta?.pagination ?? {};

  return (
    <div className="overflow-x-hidden">
      <div className="customer-scroll mx-auto max-w-[1440px] px-4 pt-[72px] md:px-[46px] md:pt-[96px]">
        <div className="mb-8 mt-6 flex flex-col items-center justify-between gap-[24px] md:mt-[52px] md:gap-[90px] lg:flex-row lg:items-start">
          <div className="ml-0 max-w-[492px] text-center lg:ml-[44px] lg:text-left">
            <h1 className=" text-[32px] font-normal leading-[120%] md:text-[64px] lg:text-[80px]">
              <span className="block">WHAT’S</span>
              <span className="block">
                <span className="text-brand-500 font-extrabold">NEW</span> ON
              </span>
              <span className="block">RAIDENX</span>
            </h1>
            <div className=" heading-md-medium-18 mt-4 flex items-center justify-center gap-1 lg:justify-start">
              <Link
                href="/"
                className="hover:underline"
                rel="noopener noreferrer"
              >
                Home
              </Link>
              <span>/</span>
              <Link
                href="/blogs"
                className="hover:underline"
                rel="noopener noreferrer"
              >
                Blog
              </Link>
            </div>
          </div>

          {!!blogs.length && <BannerSlide blogs={blogs} />}
        </div>

        <div className="mb-[32px] ml-0 px-0 md:mb-[56px] md:ml-[4px] md:px-[42px]">
          <h2 className=" mb-[12px] text-center text-[24px] font-bold sm:text-[36px] md:mb-[56px] md:text-[48px] lg:text-left lg:text-[64px]">
            All Blogs
          </h2>

          {!!blogs.length ? (
            <>
              <div className="desktop:gap-[56px] grid grid-cols-1 gap-[32px] md:grid-cols-2 lg:grid-cols-3">
                {blogs.map((post: any, i: number) => (
                  <div key={i} className="tablet:w-[383px] w-full">
                    <BlogsCard
                      image={getBlogImageUrl(
                        post?.attributes?.cover?.data?.attributes?.url
                      )}
                      title={post?.attributes?.title}
                      slug={post?.attributes?.slug}
                      description={post?.attributes?.description}
                      date={new Date(
                        post?.attributes?.publishedAt
                      ).toLocaleDateString("en-US", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}
                    />
                  </div>
                ))}
              </div>

              {blogs.length < pagination?.total && (
                <div className="mb-[40px] mt-10 flex items-center justify-center md:mb-[90px]">
                  <Link
                    href={`/blogs?page=1&pageSize=${pagination?.pageSize + 3}`}
                  >
                    <AppButton size="large" variant="secondary">
                      SHOW MORE POST
                    </AppButton>
                  </Link>
                </div>
              )}
            </>
          ) : (
            <div className=" mb-4 mt-10 flex justify-center text-[28px] font-medium leading-[120%] md:font-semibold">
              Not Found
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
