"use client";

import clsx from "clsx";
import React, { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { LinkIcon } from "@/assets/icons";
import AppConnectToSign from "@/components/AppConnectToSign";
import { useMediaQuery } from "react-responsive";
import useWindowSize from "@/hooks/useWindowSize";
import DeveloperBasePage from "@/app/developer/layout/DeveloperBasePage";
import { UserPlan } from "@/components/Developer/overview/UserPlan";
// import { ListProject } from "@/components/Developer/overview/ListProject";
import { ApiKey } from "@/components/Developer/overview/ApiKey";
import { Webhook } from "@/components/Developer/overview/Webhook";
import WebhookHistoryListPart from "@/components/Developer/webhook-histories";
import config from "@/config/index";

export default function DeveloperPage() {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isMobile = useMediaQuery({ query: "(max-width: 768px)" });
  const { windowHeight } = useWindowSize();
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const heightContent = useMemo(() => {
    if (!isMobile) {
      return (windowHeight - 52 - 40 - 40 - 8 - 10 - 30 - 40) / 2;
    }
    return windowHeight - 55 - 50 - 47;
  }, [isMobile, windowHeight]);

  if (!isClient) return null;

  if (!accessToken) {
    return <AppConnectToSign />;
  }

  return (
    <DeveloperBasePage>
      <div className={clsx("mx-auto max-w-[1024px]")}>
        <div className="mb-2 flex items-center justify-between gap-2">
          <span className="text-white-0 text-lg font-semibold">Overview</span>
          <a
            href={config.homePage.link.apiDocs}
            target="_blank"
            className="text-brand-500"
          >
            <div className="flex items-center gap-2">
              <LinkIcon />
              <div className="body-sm-regular-12">API Docs</div>
            </div>
          </a>
        </div>

        <div className="flex flex-col gap-4">
          <UserPlan />
          {/*<ListProject />*/}
          <ApiKey />
          <Webhook />
          <WebhookHistoryListPart heightContent={heightContent} />
        </div>
      </div>
    </DeveloperBasePage>
  );
}
