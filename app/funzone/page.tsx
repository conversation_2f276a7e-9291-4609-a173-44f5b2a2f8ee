"use client";

import { BoxQuickBuy, MyPositions, PairListType } from "@/components/ListPair";
import * as React from "react";
import { useCallback, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import Link from "next/link";
import { RootState } from "@/store";
import { usePathname } from "next/navigation";

import clsx from "clsx";
import { useMediaQuery } from "react-responsive";
import {
  SOCKETS_ROOMS,
  subscribeSocketRoom,
  unsubscribeSocketRoom,
} from "@/libs/socket";
import {
  AboutGraduate,
  Graduated,
  NewCreations,
} from "@/components/FunZone/parts";
import { NETWORKS } from "@/utils/contants";
import {
  ButtonFilter,
  DEXES_DEFAULT,
} from "@/components/FunZone/ButtonFillter";
import Storage from "@/libs/storage";
import { FUNZONE_FILTER_STORAGE_KEY } from "@/constants";
import { useNetwork } from "@/context/network";

const TAB_MOBILE = [
  {
    name: "New",
    id: "new",
  },
  {
    name: "Bonding",
    id: "aboutGraduate",
  },
  {
    name: "Graduated",
    id: "graduated",
  },
];

export default function MemeZonePage() {
  const { currentNetwork } = useNetwork();
  const pathname = usePathname();
  const isHomePage = pathname === "/";
  const [buyAmount, setBuyAmount] = useState<any>("1"); // set default buy amount
  const [activeTabMobile, setActiveTabMobile] = useState<string>("new"); // set default buy amount
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const isExternalWallet = useSelector(
    (state: RootState) => state.user.isExternalWallet
  );
  const isTablet = useMediaQuery({ query: "(max-width: 992px)" });
  const [isClient, setIsClient] = useState(false);
  const connectedSocket = useSelector(
    (state: RootState) => state.metadata.connectedSocket
  );
  const [filterParams, setFilterParams] = useState<any>({
    dexes: DEXES_DEFAULT,
    ...Storage.getMemeSearch(FUNZONE_FILTER_STORAGE_KEY),
  });

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    if (!connectedSocket) return;
    subscribeSocketRoom(currentNetwork, SOCKETS_ROOMS.ALL_PAIR(currentNetwork));
    return () => {
      unsubscribeSocketRoom(
        currentNetwork,
        SOCKETS_ROOMS.ALL_PAIR(currentNetwork)
      );
    };
  }, [connectedSocket, currentNetwork]);

  const _renderTabMobile = React.useCallback(() => {
    return (
      <>
        <div className="flex items-center p-[4px]">
          <ButtonFilter
            params={filterParams}
            setParams={setFilterParams}
            height={32}
            title={"Filters"}
          />
          {TAB_MOBILE.map((item) => {
            const isActive = activeTabMobile === item.id;
            return (
              <div
                key={item?.id}
                className={clsx(
                  "p-[4px] text-[12px]",
                  isActive ? "text-white-0 font-semibold" : "text-white-500 "
                )}
                onClick={() => setActiveTabMobile(item.id)}
              >
                {item?.name}
              </div>
            );
          })}
        </div>
      </>
    );
  }, [activeTabMobile]);

  const _renderContentTab = () => {
    if (activeTabMobile === "new")
      return (
        <NewCreations
          buyAmount={buyAmount}
          setBuyAmount={setBuyAmount}
          renderTabMobile={_renderTabMobile}
          isHomePage={isHomePage}
          filterParams={filterParams}
        />
      );
    if (activeTabMobile === "aboutGraduate")
      return (
        <AboutGraduate
          buyAmount={buyAmount}
          setBuyAmount={setBuyAmount}
          renderTabMobile={_renderTabMobile}
          isHomePage={isHomePage}
          filterParams={filterParams}
        />
      );
    return (
      <Graduated
        buyAmount={buyAmount}
        setBuyAmount={setBuyAmount}
        renderTabMobile={_renderTabMobile}
        isHomePage={isHomePage}
        filterParams={filterParams}
      />
    );
  };

  if (!isClient) return null;

  if (isTablet) {
    return (
      <>
        <div className="px-[8px]">
          <PairListType className="mt-[12px]" />
        </div>

        <div className="mb-[40px]">{_renderContentTab()}</div>
      </>
    );
  }

  return (
    <>
      {!isHomePage && <MyPositions />}

      <div className="mx-[8px] mb-[16px] flex justify-between gap-4 pt-[12px] md:mx-[20px]">
        <div
          className={`border-white-50 flex w-full items-center gap-[20px] border-b md:w-1/2 ${
            accessToken ? "pb-0" : "pb-2"
          }`}
        >
          <Link href={"/new-pairs"}>
            <div className="text-white-800 mb-[-8px] cursor-pointer text-[14px] font-medium leading-[1.2] md:text-[18px]">
              New Pair
            </div>
          </Link>

          <div className="text-brand-500 mb-[-6px] flex flex-col items-center text-[18px] font-semibold leading-[1.2] md:mb-[-8px] md:text-[24px]">
            Fun Zone
            <div className="bg-brand-500 h-[2px] w-[24px] rounded-[100px]"></div>
          </div>
          <Link href={"/trending"}>
            <div className="text-white-800 mb-[-8px] cursor-pointer text-[14px] font-medium leading-[1.2] md:text-[18px]">
              Trending
            </div>
          </Link>
        </div>
        <div className="flex items-center gap-[8px]">
          <ButtonFilter
            params={filterParams}
            setParams={setFilterParams}
            title={"Filters"}
          />
          {(accessToken || isExternalWallet) && (
            <BoxQuickBuy buyAmount={buyAmount} setBuyAmount={setBuyAmount} />
          )}
        </div>
      </div>

      <div className="tablet:grid hidden grid-cols-3">
        <NewCreations
          buyAmount={buyAmount}
          filterParams={filterParams}
          setBuyAmount={setBuyAmount}
          isHomePage={isHomePage}
        />
        <AboutGraduate
          filterParams={filterParams}
          buyAmount={buyAmount}
          setBuyAmount={setBuyAmount}
          isHomePage={isHomePage}
        />
        <Graduated
          buyAmount={buyAmount}
          setBuyAmount={setBuyAmount}
          isHomePage={isHomePage}
          filterParams={filterParams}
        />
      </div>
    </>
  );
}
