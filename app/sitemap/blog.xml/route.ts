import { getServerSideSitemap } from "next-sitemap";

const STRAPI_API = `${process.env.STRAPI_API_URL}/blogs?pagination[page]=1&pagination[pageSize]=1000`;
const ACCESS_TOKEN = process.env.STRAPI_ACCESS_TOKEN;

console.log(STRAPI_API, "STRAPI_API");
console.log(ACCESS_TOKEN, "ACCESS_TOKEN");

export async function GET(request: Request) {
  try {
    const response = await fetch(`${STRAPI_API}`, {
      headers: {
        Authorization: `Bearer ${ACCESS_TOKEN}`,
      },
    });
    const data = await response.json();

    const sitemaps = data.data?.map((blog: any) => {
      return {
        loc: `https://raidenx.io/blogs/${blog?.attributes?.slug}`,
        lastmod: new Date().toISOString(),
        changefreq: "daily",
        priority: 0.8,
      };
    });

    return getServerSideSitemap(sitemaps);
  } catch (error) {
    console.error("Error generating sitemap blog:", error);
    return new Response("Error generating sitemap", { status: 500 });
  }
}
