@import "tailwindcss";

@font-face {
  font-family: "Mona-Sans";
  src: local("Mona-Sans"), url("/fonts/Mona-Sans-Light.ttf") format("truetype");
  font-weight: 300;
}

@font-face {
  font-family: "Mona-Sans";
  src: local("Mona-Sans"),
    url("/fonts/Mona-Sans-Regular.ttf") format("truetype");
  font-weight: 400;
}

@font-face {
  font-family: "Mona-Sans";
  src: local("Mona-Sans"), url("/fonts/Mona-Sans-Medium.ttf") format("truetype");
  font-weight: 500;
}

@font-face {
  font-family: "Mona-Sans";
  src: local("Mona-Sans"),
    url("/fonts/Mona-Sans-SemiBold.ttf") format("truetype");
  font-weight: 600;
}

@font-face {
  font-family: "Mona-Sans";
  src: local("Mona-Sans"), url("/fonts/Mona-Sans-Bold.ttf") format("truetype");
  font-weight: 700;
}

body {
  margin: 0;
  padding: 0;
  font-family: sans-serif;
}

.Toastify__toast {
  padding: 0 !important;
  box-shadow: none !important;
}

.Toastify__toast-theme--light {
  background: transparent !important;
  padding: 0;
  box-shadow: none;
  margin-bottom: 0;
}

.hide-scroll::-webkit-scrollbar {
  display: none;
}

.customer-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.customer-scroll::-webkit-scrollbar {
  width: 2px;
  background: transparent;
  cursor: pointer;
}

.customer-scroll::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
}

.tooltip-marker .rc-tooltip-inner {
  padding: 0;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  background: linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.05) 0%,
      rgba(255, 255, 255, 0.05) 100%
    ),
    #08090c;
  backdrop-filter: blur(7.5px);
}

.rc-tooltip-placement-top,
.rc-tooltip-placement-left,
.rc-tooltip-placement-right,
.rc-tooltip-placement-bottom {
  padding: 5px !important;
}

@keyframes newTransaction {
  from {
    opacity: 0;
    transform: translateX(-50px) translateZ(0px);
  }
  to {
    opacity: 1;
    transform: none;
  }
}

.animate-new-transaction {
  animation: newTransaction 1s ease-out;
}

.track.track-0 {
  background: #ffffff;
  height: 4px;
  border-radius: 0 100px 100px 0;
}

.grecaptcha-badge {
  visibility: hidden;
}

.custom-opacity-svg-inactive {
  fill-opacity: 0.5;
}

.custom-opacity-svg-active {
  fill-opacity: 1;
}

.custom-opacity-svg:hover {
  fill-opacity: 1;
}

.active-tab:hover svg {
  color: white;
  fill-opacity: 1;
}

.active-tab:hover svg {
  fill-opacity: 1;
}

.bg-gradient-text {
  background: var(
    --gradient-text,
    linear-gradient(
      270deg,
      var(--White-300, rgba(255, 255, 255, 0.3)) 19.27%,
      var(--White-800, rgba(255, 255, 255, 0.8)) 59.64%,
      var(--White-300, rgba(255, 255, 255, 0.3)) 100%
    )
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.bg-gradient-text-2 {
  background: linear-gradient(
    90deg,
    var(--Brand-200, #99ffeb) 37.15%,
    var(--Blue-500, #80b2ff) 63.26%
  );
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

:root {
  font-size: 14px;
  line-height: 1.333;
  font-weight: 500;
  color: #ffffff;
  background: #06070e;
  --height-header-desktop: 52px;
  --height-header-mobile: 55px;
  --width-snipe-form: 324px;
}

th {
  font-weight: 400 !important;
}

.thead {
  @apply items-center gap-2 px-[8px] py-[6px];
  display: flex !important;
}

.td {
  @apply items-center gap-1 px-[8px] py-[10px];
  display: flex !important;
}

@layer base {
  .body-xs-regular-8 {
    font-size: 8px;
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
  }
  .body-xs-medium-8 {
    font-size: 8px;
    font-style: normal;
    font-weight: 500;
    line-height: 14px;
  }
  .body-xs-regular-9 {
    font-size: 9px;
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
  }
  .body-xs-medium-9 {
    font-size: 9px;
    font-style: normal;
    font-weight: 500;
    line-height: 14px;
  }
  .body-xs-regular-10 {
    font-size: 10px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
  }

  .body-xs-medium-10 {
    font-size: 10px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
  }

  .body-sm-semibold-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 18px;
  }

  .body-sm-regular-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
  }

  .body-sm-medium-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
  }

  .action-sm-medium-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
  }

  .action-md-medium-14 {
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
  }

  .action-sm-medium-14 {
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
  }

  .action-sm-semibold-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 16px;
  }

  .action-xs-medium-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
  }

  .body-md-semibold-14 {
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 20px;
  }

  .body-md-medium-14 {
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 20px;
  }

  .body-md-regular-12 {
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }

  .body-md-regular-14 {
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }

  .heading-lg-semibold-24 {
    font-size: 24px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }

  .heading-lg-semibold-32 {
    font-size: 32px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }

  .heading-md-medium-24 {
    font-size: 24px;
    font-style: normal;
    font-weight: 500;
    line-height: 120%;
  }

  .heading-md-medium-18 {
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }

  .heading-md-medium-20 {
    font-size: 20px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }

  .heading-sm-medium-16 {
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
  }

  .heading-xlg-semibold-32 {
    font-size: 32px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }

  .body-2xs-regular-8 {
    font-size: 8px;
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
  }

  .heading-2xl-semibold-40 {
    font-size: 40px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }

  .body-sm-regular-11 {
    font-size: 11px;
    font-style: normal;
    font-weight: 500;
    line-height: 18px;
  }

  .heading-sm-semibold-16 {
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }

  .heading-md-semibold-18 {
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 120%;
  }
}

@theme {
  /*black*/
  --color-black-100: #000;
  --color-black-300: rgba(8, 9, 12, 0.3);
  --color-black-500: rgba(8, 9, 12, 0.5);
  --color-black-700: rgba(8, 9, 12, 0.7);
  --color-black-800: rgba(8, 9, 12, 0.8);
  --color-black-900: #08090c;
  --black-500: #08090c80;

  /*white*/
  --color-white-0: #ffffff;
  --color-white-20: rgba(255, 255, 255, 0.2);
  --color-white-25: rgba(255, 255, 255, 0.02);
  --color-white-40: rgba(255, 255, 255, 0.4);
  --color-white-50: rgba(255, 255, 255, 0.05);
  --color-white-100: rgba(255, 255, 255, 0.1);
  --color-white-150: rgba(255, 255, 255, 0.15);
  --color-white-200: rgba(255, 255, 255, 0.2);
  --color-white-300: rgba(255, 255, 255, 0.3);
  --color-white-400: rgba(255, 255, 255, 0.4);
  --color-white-500: rgba(255, 255, 255, 0.5);
  --color-white-600: rgba(255, 255, 255, 0.6);
  --color-white-700: rgba(255, 255, 255, 0.7);
  --color-white-800: rgba(255, 255, 255, 0.8);
  --color-white-900: rgba(255, 255, 255, 0.9);
  --color-white-1000: #ffffff;

  /*brand*/
  --color-brand-50: #e6fefe;
  --color-brand-100: #cdfcfe;
  --color-brand-300: #66ffe0;
  --color-brand-400: #06f1f9;
  --color-brand-500: #0fc;
  --color-brand-600: rgba(0, 204, 163, 0.9);
  --color-brand-700: rgba(0, 204, 163, 0.6);
  --color-brand-800: rgba(0, 204, 163, 0.3);
  --color-brand-900: rgba(0, 204, 163, 0.1);
  --color-brand-1000: #012223;

  /*primary*/
  --color-primary-25: #f7f8fd;
  --color-primary-200: #bec7f4;
  --color-primary-500: #425ef0;

  /*neutral*/
  --color-neutral-0: #ffffff;
  --color-neutral-25: #fafafa;
  --color-neutral-50: #f2f3f3;
  --color-neutral-100: #e4e7e7;
  --color-neutral-200: #c9cece;
  --color-neutral-300: #afb6b6;
  --color-neutral-400: #9b9997;
  --color-neutral-500: #6d7878;
  --color-neutral-600: #495050;
  --color-neutral-700: #313636;
  --color-neutral-800: #181b1b;
  --color-neutral-900: #06070e;
  --color-neutral-1000: #0d0d0c;
  --color-neutral-1100: #080807;

  /*neutral alpha*/
  --color-neutral-alpha-20: rgba(255, 255, 255, 0.02);
  --color-neutral-alpha-50: rgba(255, 255, 255, 0.05);
  --color-neutral-alpha-100: rgba(255, 255, 255, 0.1);
  --color-neutral-alpha-150: rgba(255, 255, 255, 0.15);
  --color-neutral-alpha-200: rgba(255, 255, 255, 0.2);
  --color-neutral-alpha-300: rgba(255, 255, 255, 0.3);
  --color-neutral-alpha-400: rgba(255, 255, 255, 0.4);
  --color-neutral-alpha-500: rgba(255, 255, 255, 0.5);
  --color-neutral-alpha-600: rgba(255, 255, 255, 0.6);
  --color-neutral-alpha-700: rgba(255, 255, 255, 0.7);
  --color-neutral-alpha-800: rgba(255, 255, 255, 0.8);
  --color-neutral-alpha-900: rgba(255, 255, 255, 0.9);
  --color-neutral-alpha-1000: rgba(255, 255, 255, 1);

  /*neutral beta*/
  --color-neutral-beta-100: rgba(0, 0, 0, 0.1);
  --color-neutral-beta-200: rgba(0, 0, 0, 0.2);
  --color-neutral-beta-400: rgba(0, 0, 0, 0.4);
  --color-neutral-beta-500: rgba(6, 7, 14, 0.5);
  --color-neutral-beta-800: rgba(6, 7, 14, 0.8);
  --color-neutral-beta-900: #06070e;

  /*neutral grey*/
  --color-neutral-grey-0: #ffffff;
  --color-neutral-grey-25: #fafafa;
  --color-neutral-grey-50: #f4f4f6;
  --color-neutral-grey-75: #eeeef1;
  --color-neutral-grey-100: #e3e3e8;
  --color-neutral-grey-300: #ababba;
  --color-neutral-grey-400: #8f8fa3;
  --color-neutral-grey-500: #73738c;
  --color-neutral-grey-700: #454554;
  --color-neutral-grey-800: #2e2e38;
  --color-neutral-grey-900: #17171c;

  /*overlay*/
  --color-overlay-600: #141518;

  /*accent green*/
  --color-accent-green-50: #ebf9f3;
  --color-accent-green-500: #32b379;
  --color-accent-green-900: #22dd70;

  /*accent blue*/
  --color-accent-blue-50: #ebf5ff;
  --color-accent-blue-500: #0288fd;

  /*accent red*/
  --color-accent-red-50: #fce9e8;
  --color-accent-red-500: #ff6676;
  --color-accent-red-600: rgba(204, 62, 76, 0.9);
  --color-accent-red-700: rgba(161, 60, 69, 1);
  --color-accent-red-900: #542226;

  /*accent yellow*/
  --color-accent-yellow-50: #fef4e7;
  --color-accent-yellow-500: #f2930d;

  /*accent purple*/
  --color-accent-purple-50: #f1e8fc;
  --color-accent-purple-500: #aa75f0;

  /*accent grey*/
  --color-accent-grey-50: #f4f4f6;
  --color-accent-grey-500: #8f8fa3;

  /*blue*/
  --color-blue-50: #ebf5ff;
  --color-blue-500: #6ba6ff;

  /*orange*/
  --color-orange-50: #fdf2e8;
  --color-orange-500: #e98016;
  --color-orange-700: rgba(246, 139, 30, 0.6);
  --color-orange-800: #f68b1e4d;
  --color-orange-900: #f68b1e1a;

  /*red*/
  --color-red-50: #feedec;
  --color-red-100: #fcd1ce;
  --color-red-400: #fcd1ce;
  --color-red-500: #ff6676;
  --color-red-600: rgba(204, 62, 76, 0.9);
  --color-red-700: rgba(204, 62, 76, 0.6);
  --color-red-800: rgba(204, 62, 76, 0.3);
  --color-red-900: rgba(255, 102, 118, 0.1);

  /*purple*/
  --color-purple-50: #f2eefb;
  --color-purple-500: #572fd0;

  /*violet*/
  --color-violet-50: #f2eefb;
  --color-violet-500: #572fd0;

  /*green*/
  --color-green-50: #effbef;
  --color-green-100: #d6f4d8;
  --color-green-500: #27d971;
  --color-green-600: rgba(31, 174, 91, 0.9);
  --color-green-800: rgba(31, 174, 91, 0.3);
  --color-green-900: rgba(39, 217, 113, 0.1);

  /*yellow*/
  --color-yellow-50: #fff9eb;
  --color-yellow-100: #fff1cc;
  --color-yellow-500: #f68b1e;
  --color-yellow-700: rgba(246, 139, 30, 0.6);
  --color-yellow-900: rgba(246, 139, 30, 0.1);

  /*grey*/
  --color-grey-100: #f0f2f5;

  /*azure*/
  --color-azure-70: #a7adbe;

  /*breakpoint*/
  --breakpoint-sm: 375px;
  --breakpoint-md: 768px;
  --breakpoint-tablet: 992px;
  --breakpoint-desktop: 1025px;
  --breakpoint-lg: 1440px;
  --breakpoint-xl: 1920px;

  /*radius*/
  --radius-4: 4px;
  --radius-6: 6px;
  --radius-8: 8px;
  --radius-10: 10px;

  /*radius*/
  --shadow-inner-custom: 0px 0px 9px 0px rgba(0, 204, 163, 0.6),
    0px 0.25px 3.25px 0px #66ffe0 inset;
  --shadow-button-primary: inset 0px 0px 16px 0px rgba(193, 193, 193, 0.2);
  --shadow-button-secondary: inset 0px 0px 16px 0px rgba(0, 255, 204, 0.2);
  --shadow-menu-mobile: 4px 0px 10px 0px rgba(0, 255, 204, 0.1);

  /*container*/
  --container-1440: 90rem;
  --container-560: 35rem;
  --container-640: 40rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

* {
  font-family: "Mona-Sans", sans-serif;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: "Mona-Sans", sans-serif;
  font-size: 14px;
}

.text-custom-gradient {
  background: linear-gradient(
    108.4deg,
    #6e6e6e 0%,
    #ffffff 35%,
    #6e6e6e 70%,
    #ffffff 100%
  );
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

input[type="range"].slider-custom {
  -webkit-appearance: none;
  appearance: none;
  width: 100%;
  height: 4px;
  background: transparent;
  cursor: pointer;
  position: relative;
}

/* Track  */
input[type="range"].slider-custom::-webkit-slider-runnable-track {
  height: 4px;
  border-radius: 999px;
  background: linear-gradient(
      to right,
      #ffffff var(--range-progress, 0%),
      rgba(255, 255, 255, 0.2) var(--range-progress, 0%)
    ),
    linear-gradient(0deg, #08090c, #08090c);
}

input[type="range"].slider-custom::-moz-range-track {
  height: 4px;
  border-radius: 999px;
  background: linear-gradient(
      to right,
      #ffffff var(--range-progress, 0%),
      rgba(255, 255, 255, 0.2) var(--range-progress, 0%)
    ),
    linear-gradient(0deg, #08090c, #08090c);
}

/* Thumb */
input[type="range"].slider-custom::-webkit-slider-thumb {
  -webkit-appearance: none;
  height: 10px;
  width: 10px;
  border-radius: 999px;
  background: #5f5f5f;
  border: 1px solid #ffffff33;
  margin-top: -3px;
  position: relative;
  z-index: 2;
  transition: all 0.2s ease;
}

input[type="range"].slider-custom::-moz-range-thumb {
  height: 10px;
  width: 10px;
  border-radius: 999px;
  background: #5f5f5f;
  border: 1px solid #ffffff33;
  position: relative;
  z-index: 2;
  transition: all 0.2s ease;
}

.tooltip-volume {
  position: absolute;
  bottom: 100%;
  transform: translateX(-50%);
  background: #212224;
  color: white;
  padding: 2px 4px;
  border-radius: 4px;
  white-space: nowrap;
  z-index: 10;
  font-weight: normal;
  font-size: 8px;
  line-height: 14px;
  box-shadow: 0px 0px 4px 0px var(--black-500);
}

.tooltip-volume::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -6px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #212224;
}

.bg-custom-wallet {
  background: linear-gradient(0deg, #08090c, #08090c),
    linear-gradient(0deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1));
}

.toc-scroll {
  overflow-x: hidden;
  overflow-y: auto;
}

.toc-scroll::-webkit-scrollbar {
  width: 4px;
}

.toc-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 2px;
}

.toc-scroll::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.toc-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
@keyframes newTransaction {
  from {
    opacity: 0;
    transform: translateX(-50px) translateZ(0px);
  }
  to {
    opacity: 1;
    transform: none;
  }
}
.animate-new-transaction {
  animation: newTransaction 1s ease-out;
}

/* Blog Background Optimization */
div.blog-background {
  position: relative;
  width: 100%;
  min-height: 100vh;
}

div.blog-background::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("/images/main-background.png");
  background-size: cover;
  background-position: center center;
  background-repeat: no-repeat;
  background-attachment: scroll;
  opacity: 0.3;
  z-index: -2;
}

div.blog-background::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(8, 9, 12, 0.9) 0%,
    rgba(8, 9, 12, 0.8) 50%,
    rgba(8, 9, 12, 0.9) 100%
  );
  z-index: -1;
}

/* Responsive background adjustments */
@media (max-width: 768px) {
  div.blog-background::before {
    background-size: contain;
    background-position: top center;
    opacity: 0.2;
  }

  div.blog-background::after {
    background: linear-gradient(
      135deg,
      rgba(8, 9, 12, 0.95) 0%,
      rgba(8, 9, 12, 0.9) 50%,
      rgba(8, 9, 12, 0.95) 100%
    );
  }
}

@media (min-width: 1440px) {
  div.blog-background::before {
    background-size: 100% auto;
    background-position: top center;
    opacity: 0.4;
  }
}

/* Ensure background doesn't break on very large screens */
@media (min-width: 1920px) {
  div.blog-background::before {
    background-size: cover;
    background-position: center center;
    opacity: 0.3;
  }
}

/* Blog Content Markdown Styling */
.blog-content {
  color: #ffffffe5;
  line-height: 1.7;
  position: relative;
  z-index: 1;
}

.blog-content h1,
.blog-content h2,
.blog-content h3,
.blog-content h4,
.blog-content h5,
.blog-content h6 {
  color: #ffffff;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.blog-content h1 {
  font-size: 2.25rem;
  line-height: 1.2;
}

.blog-content h2 {
  font-size: 1.875rem;
  line-height: 1.3;
}

.blog-content h3 {
  font-size: 1.5rem;
  line-height: 1.4;
}

.blog-content h4 {
  font-size: 1.25rem;
  line-height: 1.4;
}

.blog-content p {
  margin-bottom: 1.5rem;
  font-size: 1rem;
  line-height: 1.2;
  font-weight: 400;
}

.blog-content ul,
.blog-content ol {
  margin-bottom: 1.5rem;
  padding-left: 1.5rem;
  font-size: 1rem;
  line-height: 1.2;
  font-weight: 400;
}

.blog-content li {
  margin-bottom: 0.5rem;
  font-size: 1rem;
  line-height: 1.2;
  font-weight: 400;
}

.blog-content ul li {
  list-style-type: disc;
}

.blog-content ol li {
  list-style-type: decimal;
}

.blog-content strong {
  color: #ffffff;
  font-weight: 600;
}

.blog-content em {
  font-style: italic;
}

.blog-content code {
  background-color: rgba(255, 255, 255, 0.1);
  color: #06f1f9;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.9em;
  font-family: "Courier New", monospace;
}

.blog-content pre {
  background-color: rgba(8, 9, 12, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin: 1.5rem 0;
  overflow-x: auto;
}

.blog-content pre code {
  background: none;
  padding: 0;
  color: #ffffffcc;
  font-size: 0.9rem;
}

.blog-content blockquote {
  border-left: 4px solid #06f1f9;
  padding-left: 1.5rem;
  margin: 1.5rem 0;
  font-style: italic;
  color: #ffffff99;
}

.blog-content a {
  color: #06f1f9;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.blog-content a:hover {
  color: #c6fe00;
}

.blog-content hr {
  border: none;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  margin: 2rem 0;
}

.blog-content img {
  border-radius: 0.5rem;
  margin: 1.5rem 0;
}

.blog-content table {
  width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0.5rem;
  overflow: hidden;
}

.blog-content th,
.blog-content td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.blog-content th {
  background-color: rgba(255, 255, 255, 0.05);
  color: #ffffff;
  font-weight: 600;
}

.blog-content td {
  font-size: 1rem;
}

/* Anchor links for headings */
.blog-content .anchor {
  text-decoration: none;
  color: inherit;
  position: relative;
}

.blog-content .anchor:hover {
  color: #06f1f9;
}

.blog-content .anchor::before {
  content: "#";
  position: absolute;
  left: -1.5rem;
  color: #06f1f9;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.blog-content .anchor:hover::before {
  opacity: 1;
}

/* Code titles */
.blog-content .rehype-code-title {
  background-color: rgba(8, 9, 12, 0.9);
  color: #ffffff;
  padding: 0.75rem 1.5rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-bottom: none;
  border-radius: 0.5rem 0.5rem 0 0;
  margin-top: 1.5rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  font-weight: 600;
  font-family: "Courier New", monospace;
}

.blog-content .rehype-code-title + pre {
  margin-top: 0;
  border-radius: 0 0 0.5rem 0.5rem;
}

/* Enhanced table styling */
.blog-content table {
  background-color: rgba(8, 9, 12, 0.4);
}

.blog-content tbody tr:hover {
  background-color: rgba(255, 255, 255, 0.02);
}

/* Task lists (GitHub-style checkboxes) */
.blog-content input[type="checkbox"] {
  margin-right: 0.5rem;
  accent-color: #06f1f9;
}

.blog-content ul:has(input[type="checkbox"]) {
  list-style: none;
  padding-left: 0;
}

.blog-content li:has(input[type="checkbox"]) {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.5rem;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .blog-content h1 {
    font-size: 1.75rem;
    line-height: 1.2;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .blog-content h2 {
    font-size: 1.5rem;
    line-height: 1.3;
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
  }

  .blog-content h3 {
    font-size: 1.25rem;
    line-height: 1.4;
    margin-top: 1.25rem;
    margin-bottom: 0.5rem;
  }

  .blog-content h4 {
    font-size: 1.125rem;
    line-height: 1.4;
    margin-top: 1rem;
    margin-bottom: 0.5rem;
  }

  .blog-content p {
    font-size: 1rem;
    line-height: 1.2;
    font-weight: 400;
    margin-bottom: 1.25rem;
  }

  .blog-content li {
    font-size: 1rem;
    line-height: 1.2;
    font-weight: 400;
    margin-bottom: 0.375rem;
  }

  .blog-content ul,
  .blog-content ol {
    padding-left: 1.25rem;
    margin-bottom: 1.25rem;
  }

  .blog-content blockquote {
    padding-left: 1rem;
    margin: 1.25rem 0;
    font-size: 0.95rem;
  }

  .blog-content pre {
    padding: 0.875rem;
    font-size: 0.8rem;
    margin: 1.25rem 0;
    border-radius: 0.375rem;
    overflow-x: auto;
  }

  .blog-content code {
    padding: 0.125rem 0.375rem;
    font-size: 0.85em;
  }

  .blog-content .anchor::before {
    display: none;
  }

  .blog-content .rehype-code-title {
    padding: 0.5rem 0.875rem;
    font-size: 0.8rem;
  }

  .blog-content table {
    font-size: 0.8rem;
    margin: 1rem 0;
  }

  .blog-content th,
  .blog-content td {
    padding: 0.375rem 0.5rem;
  }

  .blog-content img {
    margin: 1rem 0;
    border-radius: 0.375rem;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .blog-content h1 {
    font-size: 1.875rem;
  }

  .blog-content h2 {
    font-size: 1.5rem;
  }

  .blog-content h3 {
    font-size: 1.25rem;
  }

  .blog-content p,
  .blog-content li {
    font-size: 1rem;
    line-height: 1.2;
    font-weight: 400;
  }
}

figure.image {
  img {
    margin: auto;
  }
}
.blog-content img {
  margin: 0 auto;
}
