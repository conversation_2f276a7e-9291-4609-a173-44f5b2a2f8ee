{"name": "vdax-interface", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prepare": "husky", "commit": "cz", "postbuild": "next-sitemap"}, "dependencies": {"@cetusprotocol/aggregator-sdk": "^1.2.0", "@flowx-finance/sdk": "^1.11.3", "@hugocxl/react-to-image": "^0.0.9", "@mysten/dapp-kit": "^0.14.44", "@mysten/sui": "^1.29.1", "@reduxjs/toolkit": "^2.7.0", "@tanstack/react-query": "^5.74.4", "@types/crypto-js": "^4.2.2", "async-retry": "^1.3.3", "axios": "^1.8.4", "bignumber.js": "^9.1.2", "classnames": "^2.5.1", "clsx": "^2.1.1", "comma-number": "^2.1.0", "copy-to-clipboard": "^3.3.3", "crypto-js": "^4.2.0", "emoji-picker-react": "^4.13.2", "html-to-image": "^1.11.13", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lottie-react": "^2.4.1", "moment": "^2.30.1", "next": "15.2.5", "next-auth": "^4.24.11", "next-pwa": "^5.6.0", "next-sitemap": "^4.2.3", "qr-code-styling": "^1.9.2", "qr-scanner": "^1.4.2", "qrcode.react": "^4.2.0", "rc-slider": "^11.1.8", "rc-tooltip": "^6.4.0", "react": "^19.0.0", "react-countdown": "^2.3.6", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.56.3", "react-loader-spinner": "^6.1.6", "react-modal": "^3.16.3", "react-modern-drawer": "^1.4.0", "react-number-format": "^5.4.4", "react-paginate": "^8.3.0", "react-redux": "^9.2.0", "react-resizable-panels": "^3.0.1", "react-responsive": "^10.0.1", "react-rnd": "^10.5.2", "react-scroll": "^1.9.3", "react-share": "^5.2.2", "react-slider": "^2.0.6", "react-toastify": "^11.0.5", "react-virtuoso": "^4.12.6", "recharts": "^2.15.3", "sass": "^1.51.0", "socket.io-client": "^4.8.1", "swiper": "^11.2.10", "uuid": "^11.1.0"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "@types/async-retry": "^1.4.9", "@types/comma-number": "^2.1.2", "@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-modal": "^3.16.3", "@types/react-scroll": "^1.8.10", "@types/react-slider": "^1.3.6", "@types/websocket": "^1.0.10", "@types/ws": "^8.18.1", "commitizen": "^4.3.1", "cz-conventional-changelog": "^3.3.0", "eslint": "^9", "eslint-config-next": "15.2.5", "husky": "^9.1.7", "lint-staged": "^15.5.2", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.2.8", "tailwindcss": "^4", "typescript": "^5"}, "lint-staged": {"**/*.{js,ts,jsx,tsx}": ["eslint --fix", "prettier --write"]}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}